'use client'

import React, {
  useEffect, useRef, useState,
} from 'react';
import Konva from 'konva';
import autosize from 'autosize';
import { VectorImageStyleGroups } from '@/common/constants';
import toast from 'react-hot-toast';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import {
  Button, TextArea,
} from '../../../atoms';
import { addCursorHandlers } from '../CanvasEditor';
import { useCanvasLoading } from '../CanvasLoadingContext';

interface VectorImagePanelProps {
  canvas: Konva.Stage | null;
  agentId?: string;
  planId?: string;
}

type VectorOptionType = {
  option: string;
  label: string;
  substyle?: string;
}

export const VectorImagePanel = ({
  canvas,
  agentId,
  planId,
}: VectorImagePanelProps) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<VectorOptionType | null>(null);
  const [seed, setSeed] = useState(Math.floor(Math.random() * 1000000));
  const [guidanceScale, setGuidanceScale] = useState(3.5);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState<'style' | 'details'>('style');
  const { trackContentEvent } = useMixpanelEvent();
  const { activeProject } = useProjectContext();
  const { 
    setVectorizing, loadingStates,
  } = useCanvasLoading();

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (style: VectorOptionType) => {
    setSelectedStyle(style);
    setCurrentStep('details');
  };

  const handleBackToStyles = () => {
    setCurrentStep('style');
  };

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setVectorizing(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/recraft-image`;

      
      let isLogoStyle = false;

      if (selectedStyle) {
        isLogoStyle = VectorImageStyleGroups.logos.styles.some(
          style => style.option === selectedStyle.option,
        );
      }

      const requestBody = {
        prompt: imagePrompt,
        width: 1024,
        height: 1024,
        count: 1,
        model: isLogoStyle ? 'recraftv2' : 'recraftv3',
        style: isLogoStyle ? 'icon' : 'vector_illustration',
        substyle: selectedStyle?.substyle,
        styleId: selectedStyle?.option || '',
        seed: seed,
        controls: {
          artistic_level: isLogoStyle ? undefined : Math.round((guidanceScale - 1) * 5 / 9),
          no_text: false,
        },
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 422 && errorData.error?.includes('NSFW')) {
          throw new Error('Content flagged as inappropriate. Please try a different prompt.');
        }
        throw new Error(errorData.error || 'Failed to generate vector image');
      }

      const result = await response.json();

      if (result.success && result.images && result.images.length > 0) {
        const imageUrl = result.images[0];

        const svgResponse = await fetch(imageUrl);
        const svgString = await svgResponse.text();
        await addSvgToCanvas(svgString);
        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle?.option || 'none',
        });

        toast.success('Vector image generated and added to canvas!');
      } else {
        throw new Error('No image data received from Recraft');
      }
    } catch (error: unknown) {
      console.error('Error generating vector image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate vector image. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setVectorizing(false);
    }
  };

  const addSvgToCanvas = async (svgString: string) => {
    if (!canvas) {
      return;
    }

    try {
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
      const svgUrl = URL.createObjectURL(svgBlob);

      const imageObj = new window.Image();
      imageObj.onload = () => {
        const konvaImage = new Konva.Image({
          image: imageObj,
          draggable: true,
        });

        let layer = canvas.findOne('Layer') as Konva.Layer;
        if (!layer) {
          layer = new Konva.Layer();
          canvas.add(layer);
        }

        const canvasWidth = canvas.width();
        const canvasHeight = canvas.height();
        const imageWidth = imageObj.naturalWidth;
        const imageHeight = imageObj.naturalHeight;

        let isLogoStyle = false;
        if (selectedStyle) {
          isLogoStyle = VectorImageStyleGroups.logos.styles.some(
            style => style.option === selectedStyle.option,
          );
        }

        if (isLogoStyle) {
          const logoSize = 256;
          const scaleX = logoSize / imageWidth;
          const scaleY = logoSize / imageHeight;
          const scale = Math.min(scaleX, scaleY);

          konvaImage.scaleX(scale);
          konvaImage.scaleY(scale);
          konvaImage.x((canvasWidth - imageWidth * scale) / 2);
          konvaImage.y((canvasHeight - imageHeight * scale) / 2);
        } else {
          const scaleX = canvasWidth / imageWidth;
          const scaleY = canvasHeight / imageHeight;
          const scale = Math.min(scaleX, scaleY);

          konvaImage.scaleX(scale);
          konvaImage.scaleY(scale);
          konvaImage.x((canvasWidth - imageWidth * scale) / 2);
          konvaImage.y((canvasHeight - imageHeight * scale) / 2);
        }

        addCursorHandlers(konvaImage);

        layer.add(konvaImage);

        let transformer = layer.findOne('Transformer') as Konva.Transformer;
        if (!transformer) {
          transformer = new Konva.Transformer();
          layer.add(transformer);
        }

        transformer.nodes([konvaImage]);
        canvas.batchDraw();
        URL.revokeObjectURL(svgUrl);
      };
      imageObj.src = svgUrl;

      if (activeProject?.project_id && agentId) {
        const fileName = `Vector Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
        projectImageStorage.addGeneratedImage(
          activeProject.project_id,
          agentId,
          svgUrl,
          fileName,
          planId,
          imagePrompt,
        ).then(() => {
          window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
        }).catch((error) => {
          console.error('Error storing vector image:', error);
        });
      }

      trackContentEvent('image', {
        prompt: imagePrompt,
        imageStyle: selectedStyle?.option || 'none',
      });
    } catch (error: unknown) {
      console.error('Error adding SVG to canvas:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add vector image to canvas';
      toast.error(errorMessage);
    }
  };

  return (
    <div className="px-6 py-4">
      {currentStep === 'style' ? (
        <>
          <div className="mb-4">
            <h3 className="text-white font-semibold text-lg">Choose Vector Style</h3>
            <p className="text-gray-400 text-sm">Create vector illustrations using AI</p>
          </div>
          <div className="flex flex-col gap-4">
            <div className='flex-1'>
              <div className="pr-2">
                {Object.entries(VectorImageStyleGroups).map(([groupKey, group], groupIndex) => (
                  <div key={groupKey} className={`${groupIndex > 0 ? 'mt-8' : ''} mb-6`}>
                    <div className="sticky top-0 pt-4 bg-neutral-900 pb-2 mb-3 z-10">
                      <h4 className="text-gray-300 text-xs font-semibold uppercase tracking-wide border-b border-neutral-700 pb-2">
                        {group.title}
                      </h4>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      {group.styles.map((style) => (
                        <button
                          key={style.option}
                          onClick={() => handleStyleSelect(style)}
                          className={`p-3 rounded-xl border-2 transition-all duration-200 text-left ${
                            selectedStyle?.option === style.option
                              ? 'border-violets-are-blue bg-violets-are-blue/10'
                              : 'border-neutral-600 hover:border-violets-are-blue bg-neutral-800 hover:bg-neutral-700'
                          }`}
                        >
                          <div className="text-white text-sm font-medium mb-1">
                            {style.label}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : currentStep === 'details' ? (
        <>
          <div className="mb-4">
            <div className="flex items-center gap-3 mb-2">
              <div>
                <h3 className="text-white font-semibold text-lg">Describe Your Vector Image</h3>
                <p className="text-gray-400 text-sm">
                Style: <span className="text-violets-are-blue">{selectedStyle?.label}</span>
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col space-y-4">
            <label htmlFor='image-prompt' className="text-white font-medium text-sm">
              Image Description
              <TextArea
                id="image-prompt"
                name="vector-image-prompt"
                ref={imagePromptRef}
                value={imagePrompt}
                width='w-full'
                onChange={(e) => setImagePrompt(e.target.value)}
                placeholder="Describe the vector image you want to create..."
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey && !loadingStates.isGenerating && imagePrompt.trim()) {
                    e.preventDefault();
                    handleGenerate();
                  }
                }}
              />
            </label>

            <div>
              <label className="text-white text-sm font-medium mb-2 flex justify-between">
                <span>Seed: {seed}</span>
                <Button
                  variant='outline-rounded'
                  size="xs"
                  onClick={() => setSeed(Math.floor(Math.random() * 1000000))}
                >
                  Random
                </Button>
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="1"
                  max="1000000"
                  value={seed}
                  onChange={(e) => setSeed(Number(e.target.value))}
                  className="flex-1 accent-violets-are-blue"
                />
              </div>
            </div>
            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Guidance Scale: {guidanceScale}
              </label>
              <input
                type="range"
                min="0"
                max="20"
                step="0.5"
                value={guidanceScale}
                onChange={(e) => setGuidanceScale(Number(e.target.value))}
                className="w-full accent-violets-are-blue"
              />
            </div>

            {error && (
              <div className="text-tulip text-sm">
                {error}
              </div>
            )}

            <Button
              variant="gradient"
              size="md"
              width="w-full"
              onClick={handleGenerate}
              disabled={loadingStates.isVectorizing || !imagePrompt.trim()}
            >
              {loadingStates.isVectorizing ? 'Generating Vector Image...' : 'Generate Vector Image'}
            </Button>
            <Button
              onClick={handleBackToStyles}
              variant='outline'
              size='md'
              width='w-full'
            >
              Back
            </Button>
          </div>
        </>
      ) : null}
    </div>
  );
};
